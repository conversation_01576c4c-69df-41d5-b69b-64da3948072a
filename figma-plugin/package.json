{"name": "animagen-figma-plugin", "version": "2.0.0", "description": "Export Figma frames to AnimaGen for creating animated slideshows", "author": "AnimaGen Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GsusFC/anima.git", "directory": "figma-plugin"}, "keywords": ["figma", "plugin", "animation", "slideshow", "export", "animagen"], "dependencies": {"@create-figma-plugin/utilities": "^4.0.1", "@create-figma-plugin/ui": "^4.0.1", "preact": "^10.0.0"}, "devDependencies": {"@create-figma-plugin/build": "^4.0.1", "@create-figma-plugin/tsconfig": "^4.0.1", "@figma/plugin-typings": "1.100.2", "typescript": ">=4"}, "scripts": {"build": "build-figma-plugin --typecheck --minify", "watch": "build-figma-plugin --typecheck --watch"}, "figma-plugin": {"editorType": ["figma"], "id": "animagen-exporter", "name": "AnimaGen Exporter", "main": "src/main.ts", "ui": "src/ui.tsx", "networkAccess": {"allowedDomains": ["https://anima-production-3dad.up.railway.app"], "devAllowedDomains": ["http://localhost:3000", "http://localhost:5000"]}}}