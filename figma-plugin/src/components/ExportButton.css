/* Spinner animation for export button */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(0.8);
  }
}

/* Smooth transitions for all button states */
.export-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.export-button:hover {
  transform: translateY(-1px);
}

.export-button:active {
  transform: scale(0.98);
}

.export-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Enhanced focus states for accessibility */
.export-button:focus-visible {
  outline: 2px solid #ec4899;
  outline-offset: 2px;
}
