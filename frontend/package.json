{"name": "animagen-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/react-transition-group": "^4.4.12", "@vitejs/plugin-react": "^4.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hotkeys-hook": "^5.1.0", "react-router-dom": "^7.6.3", "react-transition-group": "^4.4.5", "socket.io-client": "^4.8.1", "vite": "^6.3.5"}, "devDependencies": {"@figma/plugin-typings": "^1.115.0", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "autoprefixer": "^10.4.19", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.100.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "*"}}