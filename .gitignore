# Dependencias
node_modules/
*/node_modules/
**/node_modules/
/.pnp
.pnp.js

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# Vite build output
dist/
build/

# Rollup.js build output
dist/

# Webpack build output
build/

# Svelte kit build output
.svelte-kit

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Temporary folders
tmp/
temp/

# Logs
*.log
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Ficheros temporales y de salida generados por la app
/backend/temp/
/backend/output/
backend.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
coverage/

# Build tools
.cache/

# Vite dev cache
.vite/

# Media test files
*.mp4

# Redis dump files
dump.rdb
*.rdb

# Backend logs directory
/backend/logs/

# Development documentation (temporary files)
*_SUMMARY.md
*_ANALYSIS.md
*_REFACTOR*.md
*_MIGRATION*.md
*_IMPLEMENTATION*.md
AGENT.md
BETA_FEEDBACK*.md
INTEGRATION_DEMO.md
MEMORIA_*.md
REFACTOR_PLAN.md
VALIDATION_*.md

# Test files in root (should be in tests/)
test-*.js
debug-*.js
*test*.json

# Deployment scripts (use docs/DEPLOYMENT.md instead)
deploy-*.sh
package-plugin.sh
start-dev.sh

# Platform-specific configs (use environment variables)
railway.*
heroku.*

# Duplicate configs
postcss.config.js  # Keep only in frontend/
mcp-config.json

# Alternative server files (keep only main index.js)
*-server.js
server-simple.js

# Disabled/legacy code
src-disabled/
legacy/
deprecated/
