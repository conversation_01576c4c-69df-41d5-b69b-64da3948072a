# AnimaGen Environment Configuration
# Copy this file to .env and configure for your environment

# =============================================================================
# DEVELOPMENT ENVIRONMENT
# =============================================================================

# Node.js Environment
NODE_ENV=development

# Server Configuration
PORT=3001
HOST=localhost

# =============================================================================
# FILE HANDLING
# =============================================================================

# Storage Directories
OUTPUT_DIR=output
TEMP_DIR=uploads
UPLOADS_DIR=uploads

# File Limits
MAX_FILE_SIZE=52428800    # 50MB in bytes
MAX_FILES=50              # Maximum files per upload
MAX_DURATION=300          # Maximum video duration in seconds

# =============================================================================
# PROCESSING CONFIGURATION
# =============================================================================

# FFmpeg Settings
FFMPEG_THREADS=4          # Number of threads for FFmpeg
DEFAULT_FPS=30            # Default frames per second
PREVIEW_MAX_WIDTH=1280    # Maximum preview width
PREVIEW_MAX_HEIGHT=720    # Maximum preview height
PREVIEW_QUALITY=fast      # Preview quality: fast, standard, high

# Processing Limits
MAX_CONCURRENT_JOBS=3     # Maximum concurrent processing jobs

# =============================================================================
# REDIS CONFIGURATION (OPTIONAL)
# =============================================================================

# Redis URL for queue processing (optional - will fallback to sync processing)
# REDIS_URL=redis://localhost:6379
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=

# Queue Settings
QUEUE_ENABLED=false       # Enable queue-based processing
WORKER_CONCURRENCY=2      # Number of worker processes

# =============================================================================
# SECURITY & CORS
# =============================================================================

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:3000

# Security Features
ENABLE_PROCESSING=true    # Enable file processing
MOCK_MODE=false          # Enable mock mode for testing

# =============================================================================
# LOGGING & DEBUGGING
# =============================================================================

# Log Level
LOG_LEVEL=info           # error, warn, info, debug

# Debug Namespaces (for development)
# DEBUG=animagen:*
# DEBUG=animagen:ffmpeg,animagen:upload

# Enable Metrics
ENABLE_METRICS=false

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================

# For production, override these values:
# NODE_ENV=production
# PORT=3001
# MAX_FILE_SIZE=104857600  # 100MB
# MAX_FILES=100
# CORS_ORIGINS=https://yourdomain.com
# LOG_LEVEL=warn
# ENABLE_METRICS=true

# =============================================================================
# CLOUD DEPLOYMENT
# =============================================================================

# For cloud deployments, you may need:
# DATABASE_URL=postgresql://...
# STORAGE_BUCKET=your-s3-bucket
# CDN_URL=https://cdn.yourdomain.com
# SENTRY_DSN=https://...

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Hot Reload
CHOKIDAR_USEPOLLING=false

# TypeScript
TS_NODE_TRANSPILE_ONLY=true

# Testing
TEST_TIMEOUT=30000
